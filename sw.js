/**
 * Service Worker for iSHIUBA Website
 * Provides caching strategies for better performance
 */

const CACHE_NAME = 'ishiuba-v1.0.0';
const CACHE_VERSION = '1.0.0';

// Assets to cache immediately
const STATIC_ASSETS = [
  './',
  './index.html',
  './main.html',
  './about.html',
  './static/scss/main.css',
  './static/js/app.js',
  './static/js/languageManager.js',
  './static/js/themeManager.js',
  './static/js/navigationManager.js',
  './static/js/componentManager.js',
  './static/js/cacheManager.js',
  './static/js/videoLoader.js',
  './static/js/translations.js',
  './static/img/ishiubahor.png',
  './static/img/shiuba2.png'
];

// External resources to cache
const EXTERNAL_ASSETS = [
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css',
  'https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/7.5.0/css/flag-icons.min.css'
];

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only'
};

// Route patterns and their strategies
const ROUTE_STRATEGIES = [
  { pattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/i, strategy: CACHE_STRATEGIES.CACHE_FIRST },
  { pattern: /\.(?:js|css)$/i, strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE },
  { pattern: /\.(?:html)$/i, strategy: CACHE_STRATEGIES.NETWORK_FIRST },
  { pattern: /\/static\/json\//i, strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE },
  { pattern: /googleapis|gstatic|cdnjs|jsdelivr/i, strategy: CACHE_STRATEGIES.CACHE_FIRST }
];

/**
 * Install event - cache static assets
 */
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets...');
        return Promise.allSettled([
          cache.addAll(STATIC_ASSETS),
          cache.addAll(EXTERNAL_ASSETS)
        ]);
      })
      .then((results) => {
        const failed = results.filter(result => result.status === 'rejected');
        if (failed.length > 0) {
          console.warn('Some assets failed to cache:', failed);
        }
        console.log('Service Worker installed successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker installation failed:', error);
      })
  );
});

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker activated');
        return self.clients.claim();
      })
  );
});

/**
 * Fetch event - handle requests with caching strategies
 */
self.addEventListener('fetch', (event) => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip chrome-extension and other non-http requests
  if (!event.request.url.startsWith('http')) {
    return;
  }

  const url = new URL(event.request.url);
  const strategy = getStrategyForRequest(event.request);

  event.respondWith(
    handleRequest(event.request, strategy)
      .catch((error) => {
        console.error('Fetch error:', error);
        return new Response('Network error', { status: 503 });
      })
  );
});

/**
 * Determine caching strategy for a request
 */
function getStrategyForRequest(request) {
  const url = request.url;
  
  for (const route of ROUTE_STRATEGIES) {
    if (route.pattern.test(url)) {
      return route.strategy;
    }
  }
  
  // Default strategy
  return CACHE_STRATEGIES.NETWORK_FIRST;
}

/**
 * Handle request with specified strategy
 */
async function handleRequest(request, strategy) {
  switch (strategy) {
    case CACHE_STRATEGIES.CACHE_FIRST:
      return cacheFirst(request);
    
    case CACHE_STRATEGIES.NETWORK_FIRST:
      return networkFirst(request);
    
    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
      return staleWhileRevalidate(request);
    
    case CACHE_STRATEGIES.NETWORK_ONLY:
      return fetch(request);
    
    case CACHE_STRATEGIES.CACHE_ONLY:
      return caches.match(request);
    
    default:
      return networkFirst(request);
  }
}

/**
 * Cache First strategy
 */
async function cacheFirst(request) {
  const cache = await caches.open(CACHE_NAME);
  const cached = await cache.match(request);
  
  if (cached) {
    return cached;
  }
  
  const response = await fetch(request);
  
  if (response.status === 200) {
    cache.put(request, response.clone());
  }
  
  return response;
}

/**
 * Network First strategy
 */
async function networkFirst(request) {
  const cache = await caches.open(CACHE_NAME);
  
  try {
    const response = await fetch(request);
    
    if (response.status === 200) {
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    const cached = await cache.match(request);
    
    if (cached) {
      return cached;
    }
    
    throw error;
  }
}

/**
 * Stale While Revalidate strategy
 */
async function staleWhileRevalidate(request) {
  const cache = await caches.open(CACHE_NAME);
  const cached = await cache.match(request);
  
  // Start fetch in background
  const fetchPromise = fetch(request).then((response) => {
    if (response.status === 200) {
      cache.put(request, response.clone());
    }
    return response;
  });
  
  // Return cached version immediately if available
  if (cached) {
    return cached;
  }
  
  // Otherwise wait for network
  return fetchPromise;
}

/**
 * Message event - handle messages from main thread
 */
self.addEventListener('message', (event) => {
  if (event.data && event.data.type) {
    switch (event.data.type) {
      case 'SKIP_WAITING':
        self.skipWaiting();
        break;
      
      case 'GET_CACHE_STATS':
        getCacheStats().then((stats) => {
          event.ports[0].postMessage(stats);
        });
        break;
      
      case 'CLEAR_CACHE':
        clearCache().then(() => {
          event.ports[0].postMessage({ success: true });
        });
        break;
    }
  }
});

/**
 * Get cache statistics
 */
async function getCacheStats() {
  const cache = await caches.open(CACHE_NAME);
  const keys = await cache.keys();
  
  return {
    cacheName: CACHE_NAME,
    cacheVersion: CACHE_VERSION,
    cachedRequests: keys.length,
    cacheSize: await getCacheSize(cache)
  };
}

/**
 * Get approximate cache size
 */
async function getCacheSize(cache) {
  const keys = await cache.keys();
  let totalSize = 0;
  
  for (const request of keys) {
    try {
      const response = await cache.match(request);
      if (response) {
        const blob = await response.blob();
        totalSize += blob.size;
      }
    } catch (error) {
      // Ignore errors for individual items
    }
  }
  
  return totalSize;
}

/**
 * Clear all caches
 */
async function clearCache() {
  const cacheNames = await caches.keys();
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
}
