<!DOCTYPE html>
<html lang="br">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="iSHIUBA's about page." />
    <meta name="keywords" content="iamshiuba, music, videos, entertainment" />
    <title>Sobre</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
      integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/7.5.0/css/flag-icons.min.css"
      integrity="sha512-+WVTaUIzUw5LFzqIqXOT3JVAc5SrMuvHm230I9QAZa6s+QRk8NDPswbHo2miIZj3yiFyV9lAgzO1wVrjdoO4tw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <link rel="stylesheet" href="./static/scss/main.css" />
  </head>
  <body>
    <div class="container-fluid" id="page">
      <header>
        <nav class="navbar collapse show" id="upCollapse">
          <div class="container-fluid">
            <a class="navbar-brand">
              <img src="static\img\ishiubahor.png" alt="shiuba" />
            </a>
            <div id="nav">
              <ul class="navbar-nav">
                <li id="Homepage" class="nav-item active" aria-current="page">
                  <a class="nav-link" href="." data-translate="Homepage"
                    >Homepage</a
                  >
                </li>
                <li id="Videos" class="nav-item">
                  <a class="nav-link" href="main.html" data-translate="Videos"
                    >Videos</a
                  >
                </li>
                <li id="About" class="nav-item">
                  <a class="nav-link" href="about.html" data-translate="About"
                    >About</a
                  >
                </li>
              </ul>
            </div>
            <div
              id="langselect"
              class="btn-group btn-group-lg me-2"
              role="group"
              aria-label="Language Selector"
            >
              <button
                type="button"
                class="btn"
                data-language="en"
                title="English"
              >
                <i class="fi fi-us"></i>
              </button>
              <button
                type="button"
                class="btn"
                data-language="br"
                title="Português do Brasil"
              >
                <i class="fi fi-br"></i>
              </button>
              <button
                type="button"
                class="btn"
                data-language="jp"
                title="日本語 (Japanese)"
              >
                <i class="fi fi-jp"></i>
              </button>
            </div>
            <div
              class="btn-group btn-group-sm me-2"
              role="group"
              aria-label="Theme Selector"
            >
              <button
                type="button"
                class="btn btn-outline-secondary theme-toggle-btn"
                data-theme-toggle
                data-theme-value="light"
                title="Light Theme"
              >
                <i class="fa fa-sun"></i>
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary theme-toggle-btn"
                data-theme-toggle
                data-theme-value="dark"
                title="Dark Theme"
              >
                <i class="fa fa-moon"></i>
              </button>
            </div>
          </div>
        </nav>
        <button
          id="up"
          class="btn btn-black w-100"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#upCollapse"
          aria-expanded="false"
          aria-controls="upCollapse"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="currentColor"
            class="bi bi-arrow-down-up"
            viewBox="0 0 16 16"
          >
            <path
              fill-rule="evenodd"
              d="M11.5 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L11 2.707V14.5a.5.5 0 0 0 .5.5m-7-14a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L4 13.293V1.5a.5.5 0 0 1 .5-.5"
            />
          </svg>
        </button>
      </header>
      <main>
        <div id="about">
          <h1 data-translate="aboutTitle">About</h1>
          <p class="lead" data-translate="aboutMessage">
            I made this website for fun. Hope you enjoy coz I'm about to make it
            better and better! Thank you for visiting!
          </p>
        </div>
      </main>
      <footer>
        <button
          id="down"
          class="btn btn-black w-100"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#downCollapse"
          aria-expanded="false"
          aria-controls="downCollapse"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            fill="currentColor"
            class="bi bi-arrow-down-up"
            viewBox="0 0 16 16"
          >
            <path
              fill-rule="evenodd"
              d="M11.5 15a.5.5 0 0 0 .5-.5V2.707l3.146 3.147a.5.5 0 0 0 .708-.708l-4-4a.5.5 0 0 0-.708 0l-4 4a.5.5 0 1 0 .708.708L11 2.707V14.5a.5.5 0 0 0 .5.5m-7-14a.5.5 0 0 1 .5.5v11.793l3.146-3.147a.5.5 0 0 1 .708.708l-4 4a.5.5 0 0 1-.708 0l-4-4a.5.5 0 0 1 .708-.708L4 13.293V1.5a.5.5 0 0 1 .5-.5"
            />
          </svg>
        </button>
        <div class="collapse show" id="downCollapse">
          <div class="container">
            <p>
              Powered by NEXTせだい <br />
              &copy; 2023 - 2024 IamSHIUBA.<br />
              <span data-translate="footer">All rights reserved.</span>
            </p>
          </div>
        </div>
      </footer>
    </div>

    <!-- Floating Social Media Button -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050">
      <button
        class="btn btn-primary rounded-circle shadow-lg"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#floatingMenu"
        aria-controls="floatingMenu"
        aria-expanded="false"
        aria-label="Show social links"
        style="width: 56px; height: 56px"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          class="bi bi-box-arrow-up-right"
          viewBox="0 0 16 16"
        >
          <path
            fill-rule="evenodd"
            d="M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-1 0V14.5a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h6.636a.5.5 0 0 0 .5-.5"
          />
          <path
            fill-rule="evenodd"
            d="M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0z"
          />
        </svg>
      </button>

      <!-- Floating Menu -->
      <div
        class="collapse position-absolute bottom-100 end-0 mb-2"
        id="floatingMenu"
      >
        <div class="card shadow-lg" style="min-width: 200px">
          <div class="card-body p-2">
            <div class="d-flex flex-column gap-2">
              <a
                rel="noopener"
                href="https://github.com/ishiuba"
                class="btn btn-outline-primary btn-sm d-flex align-items-center gap-2"
                target="_blank"
              >
                <i class="fa-brands fa-github"></i>
                GitHub
              </a>
              <a
                rel="noopener"
                href="https://x.com/iamshiuba"
                class="btn btn-outline-primary btn-sm d-flex align-items-center gap-2"
                target="_blank"
              >
                <i class="fa-brands fa-x-twitter"></i>
                X (Twitter)
              </a>
              <a
                rel="noopener"
                href="https://www.youtube.com/@iamshiuba"
                class="btn btn-outline-primary btn-sm d-flex align-items-center gap-2"
                target="_blank"
              >
                <i class="fa-brands fa-youtube"></i>
                YouTube
              </a>
              <a
                rel="noopener"
                href="https://soundcloud.com/iamshiuba"
                class="btn btn-outline-primary btn-sm d-flex align-items-center gap-2"
                target="_blank"
              >
                <i class="fa-brands fa-soundcloud"></i>
                SoundCloud
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="static/js/translations.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- New modular structure -->
    <script src="static/js/videoLoader.js"></script>
    <script src="static/js/languageManager.js"></script>
    <script src="static/js/themeManager.js"></script>
    <script src="static/js/app.js"></script>
  </body>
</html>
