/**
 * NavigationManager - Handles active state management for navigation
 * Provides consistent navigation state across pages and sections
 */
class NavigationManager {
  constructor(options = {}) {
    this.options = {
      navSelector: '.navbar-nav',
      linkSelector: '.nav-link',
      activeClass: 'active',
      currentPageAttribute: 'aria-current',
      updateOnLoad: true,
      updateOnHashChange: true,
      ...options
    };

    this.currentPage = null;
    this.navElements = new Map();
    this.initialized = false;

    // Page mapping for different file structures
    this.pageMapping = {
      '': 'Homepage',
      'index.html': 'Homepage',
      'index': 'Homepage',
      'main.html': 'Videos',
      'main': 'Videos',
      'about.html': 'About',
      'about': 'About'
    };

    // Initialize if DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.init());
    } else {
      this.init();
    }
  }

  /**
   * Initialize the navigation manager
   */
  init() {
    if (this.initialized) return;

    console.log('Initializing NavigationManager...');

    // Find and cache navigation elements
    this.cacheNavigationElements();

    // Set initial active state
    this.updateActiveState();

    // Set up event listeners
    this.setupEventListeners();

    this.initialized = true;
    console.log('NavigationManager initialized successfully');
  }

  /**
   * Cache navigation elements for better performance
   */
  cacheNavigationElements() {
    const navContainer = document.querySelector(this.options.navSelector);
    if (!navContainer) {
      console.warn('Navigation container not found:', this.options.navSelector);
      return;
    }

    const links = navContainer.querySelectorAll(this.options.linkSelector);
    links.forEach(link => {
      const href = link.getAttribute('href');
      const parentLi = link.closest('.nav-item');
      const pageId = parentLi ? parentLi.id : null;
      
      if (pageId) {
        this.navElements.set(pageId, {
          link,
          parentLi,
          href,
          originalHref: href
        });
      }
    });

    console.log('Cached navigation elements:', this.navElements.size);
  }

  /**
   * Get current page identifier
   */
  getCurrentPage() {
    // Get current path
    const path = window.location.pathname;
    const filename = path.split('/').pop() || '';
    const hash = window.location.hash.slice(1);

    // Check hash first for single-page navigation
    if (hash && this.pageMapping[hash]) {
      return this.pageMapping[hash];
    }

    // Check filename
    if (this.pageMapping[filename]) {
      return this.pageMapping[filename];
    }

    // Check path without extension
    const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
    if (this.pageMapping[nameWithoutExt]) {
      return this.pageMapping[nameWithoutExt];
    }

    // Default to Homepage for root or unknown pages
    return 'Homepage';
  }

  /**
   * Update active state for navigation
   */
  updateActiveState() {
    const currentPage = this.getCurrentPage();
    
    if (this.currentPage === currentPage) {
      return; // No change needed
    }

    console.log(`Updating navigation active state: ${this.currentPage} -> ${currentPage}`);

    // Remove active state from all navigation items
    this.navElements.forEach(({ link, parentLi }) => {
      link.classList.remove(this.options.activeClass);
      parentLi.classList.remove(this.options.activeClass);
      link.removeAttribute(this.options.currentPageAttribute);
    });

    // Add active state to current page
    if (this.navElements.has(currentPage)) {
      const { link, parentLi } = this.navElements.get(currentPage);
      link.classList.add(this.options.activeClass);
      parentLi.classList.add(this.options.activeClass);
      link.setAttribute(this.options.currentPageAttribute, 'page');
    }

    this.currentPage = currentPage;

    // Dispatch custom event
    document.dispatchEvent(new CustomEvent('navigationStateChanged', {
      detail: { currentPage, previousPage: this.currentPage }
    }));
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Update on page load
    if (this.options.updateOnLoad) {
      window.addEventListener('load', () => this.updateActiveState());
    }

    // Update on hash change (for single-page navigation)
    if (this.options.updateOnHashChange) {
      window.addEventListener('hashchange', () => this.updateActiveState());
    }

    // Update on popstate (browser back/forward)
    window.addEventListener('popstate', () => {
      setTimeout(() => this.updateActiveState(), 100);
    });

    // Handle navigation clicks
    this.navElements.forEach(({ link }) => {
      link.addEventListener('click', (e) => {
        // Small delay to allow navigation to complete
        setTimeout(() => this.updateActiveState(), 50);
      });
    });
  }

  /**
   * Manually set active page (useful for dynamic content)
   */
  setActivePage(pageId) {
    if (!this.navElements.has(pageId)) {
      console.warn(`Page ID not found in navigation: ${pageId}`);
      return false;
    }

    // Update current page and refresh active state
    this.currentPage = null; // Force update
    this.pageMapping['manual'] = pageId;
    
    // Temporarily override getCurrentPage
    const originalGetCurrentPage = this.getCurrentPage;
    this.getCurrentPage = () => pageId;
    
    this.updateActiveState();
    
    // Restore original method
    this.getCurrentPage = originalGetCurrentPage;
    
    return true;
  }

  /**
   * Add custom page mapping
   */
  addPageMapping(path, pageId) {
    this.pageMapping[path] = pageId;
  }

  /**
   * Get available pages
   */
  getAvailablePages() {
    return Array.from(this.navElements.keys());
  }

  /**
   * Get current active page
   */
  getActivePage() {
    return this.currentPage;
  }

  /**
   * Refresh navigation state (useful after dynamic content changes)
   */
  refresh() {
    this.cacheNavigationElements();
    this.updateActiveState();
  }

  /**
   * Destroy the navigation manager
   */
  destroy() {
    // Remove event listeners would go here if we stored references
    this.navElements.clear();
    this.initialized = false;
  }
}

// Global navigation manager instance
let navigationManager = null;

/**
 * Initialize navigation manager
 */
function initializeNavigation(options = {}) {
  if (navigationManager) {
    navigationManager.destroy();
  }
  
  navigationManager = new NavigationManager(options);
  
  // Export to global scope for compatibility
  window.navigationManager = navigationManager;
  
  console.log('Navigation manager initialized');
  return navigationManager;
}

/**
 * Get current navigation manager instance
 */
function getNavigationManager() {
  return navigationManager;
}

/**
 * Set active page manually
 */
function setActivePage(pageId) {
  if (navigationManager) {
    return navigationManager.setActivePage(pageId);
  }
  console.warn('Navigation manager not initialized');
  return false;
}

// Auto-initialize if not in module environment
if (typeof module === 'undefined') {
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => initializeNavigation());
  } else {
    initializeNavigation();
  }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    NavigationManager,
    initializeNavigation,
    getNavigationManager,
    setActivePage
  };
}
