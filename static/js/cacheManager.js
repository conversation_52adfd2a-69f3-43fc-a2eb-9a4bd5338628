/**
 * CacheManager - <PERSON><PERSON> caching strategies for better performance
 * Implements browser caching, localStorage, and service worker coordination
 */
class CacheManager {
  constructor(options = {}) {
    this.options = {
      enableServiceWorker: true,
      enableLocalStorage: true,
      enableSessionStorage: true,
      cacheVersion: '1.0.0',
      maxCacheAge: 24 * 60 * 60 * 1000, // 24 hours
      maxCacheSize: 50, // Maximum number of cached items
      ...options
    };

    this.caches = {
      localStorage: new Map(),
      sessionStorage: new Map(),
      memory: new Map()
    };

    this.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };

    this.initialized = false;
  }

  /**
   * Initialize cache manager
   */
  async init() {
    if (this.initialized) return;

    console.log('Initializing CacheManager...');

    try {
      // Load existing caches
      this.loadExistingCaches();

      // Register service worker if supported and enabled
      if (this.options.enableServiceWorker && 'serviceWorker' in navigator) {
        await this.registerServiceWorker();
      }

      // Set up cache cleanup
      this.setupCacheCleanup();

      this.initialized = true;
      console.log('CacheManager initialized successfully');

    } catch (error) {
      console.error('Failed to initialize CacheManager:', error);
    }
  }

  /**
   * Load existing caches from storage
   */
  loadExistingCaches() {
    if (this.options.enableLocalStorage) {
      try {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.startsWith('cache_')) {
            const data = localStorage.getItem(key);
            if (data) {
              const parsed = JSON.parse(data);
              if (this.isValidCacheEntry(parsed)) {
                this.caches.localStorage.set(key, parsed);
              } else {
                localStorage.removeItem(key);
              }
            }
          }
        });
      } catch (error) {
        console.warn('Failed to load localStorage cache:', error);
      }
    }

    if (this.options.enableSessionStorage) {
      try {
        const keys = Object.keys(sessionStorage);
        keys.forEach(key => {
          if (key.startsWith('cache_')) {
            const data = sessionStorage.getItem(key);
            if (data) {
              const parsed = JSON.parse(data);
              if (this.isValidCacheEntry(parsed)) {
                this.caches.sessionStorage.set(key, parsed);
              } else {
                sessionStorage.removeItem(key);
              }
            }
          }
        });
      } catch (error) {
        console.warn('Failed to load sessionStorage cache:', error);
      }
    }
  }

  /**
   * Check if cache entry is valid
   */
  isValidCacheEntry(entry) {
    if (!entry || typeof entry !== 'object') return false;
    if (!entry.timestamp || !entry.data) return false;
    
    const age = Date.now() - entry.timestamp;
    return age < this.options.maxCacheAge;
  }

  /**
   * Get item from cache
   */
  get(key, storageType = 'memory') {
    const cacheKey = `cache_${key}`;
    const cache = this.caches[storageType];
    
    if (!cache) {
      console.warn(`Invalid storage type: ${storageType}`);
      return null;
    }

    let entry = cache.get(cacheKey);
    
    // Try to get from actual storage if not in memory cache
    if (!entry && storageType !== 'memory') {
      try {
        const storage = storageType === 'localStorage' ? localStorage : sessionStorage;
        const data = storage.getItem(cacheKey);
        if (data) {
          entry = JSON.parse(data);
          if (this.isValidCacheEntry(entry)) {
            cache.set(cacheKey, entry);
          } else {
            storage.removeItem(cacheKey);
            return null;
          }
        }
      } catch (error) {
        console.warn(`Failed to get from ${storageType}:`, error);
        return null;
      }
    }

    if (entry && this.isValidCacheEntry(entry)) {
      this.cacheStats.hits++;
      return entry.data;
    }

    this.cacheStats.misses++;
    return null;
  }

  /**
   * Set item in cache
   */
  set(key, data, storageType = 'memory', options = {}) {
    const cacheKey = `cache_${key}`;
    const entry = {
      data,
      timestamp: Date.now(),
      version: this.options.cacheVersion,
      ...options
    };

    const cache = this.caches[storageType];
    if (!cache) {
      console.warn(`Invalid storage type: ${storageType}`);
      return false;
    }

    try {
      // Set in memory cache
      cache.set(cacheKey, entry);

      // Set in actual storage if not memory
      if (storageType !== 'memory') {
        const storage = storageType === 'localStorage' ? localStorage : sessionStorage;
        storage.setItem(cacheKey, JSON.stringify(entry));
      }

      this.cacheStats.sets++;
      
      // Clean up if cache is too large
      this.cleanupCache(storageType);
      
      return true;
    } catch (error) {
      console.warn(`Failed to set cache in ${storageType}:`, error);
      return false;
    }
  }

  /**
   * Delete item from cache
   */
  delete(key, storageType = 'memory') {
    const cacheKey = `cache_${key}`;
    const cache = this.caches[storageType];
    
    if (!cache) {
      console.warn(`Invalid storage type: ${storageType}`);
      return false;
    }

    try {
      cache.delete(cacheKey);

      if (storageType !== 'memory') {
        const storage = storageType === 'localStorage' ? localStorage : sessionStorage;
        storage.removeItem(cacheKey);
      }

      this.cacheStats.deletes++;
      return true;
    } catch (error) {
      console.warn(`Failed to delete from ${storageType}:`, error);
      return false;
    }
  }

  /**
   * Clear all cache
   */
  clear(storageType = 'all') {
    if (storageType === 'all') {
      Object.keys(this.caches).forEach(type => this.clear(type));
      return;
    }

    const cache = this.caches[storageType];
    if (!cache) return;

    try {
      cache.clear();

      if (storageType !== 'memory') {
        const storage = storageType === 'localStorage' ? localStorage : sessionStorage;
        const keys = Object.keys(storage);
        keys.forEach(key => {
          if (key.startsWith('cache_')) {
            storage.removeItem(key);
          }
        });
      }
    } catch (error) {
      console.warn(`Failed to clear ${storageType}:`, error);
    }
  }

  /**
   * Clean up old cache entries
   */
  cleanupCache(storageType) {
    const cache = this.caches[storageType];
    if (!cache) return;

    // Remove expired entries
    const now = Date.now();
    const keysToDelete = [];

    cache.forEach((entry, key) => {
      if (!this.isValidCacheEntry(entry)) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => {
      cache.delete(key);
      if (storageType !== 'memory') {
        try {
          const storage = storageType === 'localStorage' ? localStorage : sessionStorage;
          storage.removeItem(key);
        } catch (error) {
          console.warn('Failed to remove expired cache entry:', error);
        }
      }
    });

    // Limit cache size
    if (cache.size > this.options.maxCacheSize) {
      const entries = Array.from(cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, cache.size - this.options.maxCacheSize);
      toRemove.forEach(([key]) => {
        this.delete(key.replace('cache_', ''), storageType);
      });
    }
  }

  /**
   * Set up periodic cache cleanup
   */
  setupCacheCleanup() {
    // Clean up every 30 minutes
    setInterval(() => {
      Object.keys(this.caches).forEach(storageType => {
        this.cleanupCache(storageType);
      });
    }, 30 * 60 * 1000);
  }

  /**
   * Register service worker
   */
  async registerServiceWorker() {
    try {
      const registration = await navigator.serviceWorker.register('./sw.js');
      console.log('Service Worker registered successfully:', registration);
      
      // Listen for service worker updates
      registration.addEventListener('updatefound', () => {
        console.log('Service Worker update found');
      });
      
    } catch (error) {
      console.warn('Service Worker registration failed:', error);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const totalEntries = Object.values(this.caches).reduce((sum, cache) => sum + cache.size, 0);
    const hitRate = this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) || 0;
    
    return {
      ...this.cacheStats,
      totalEntries,
      hitRate: Math.round(hitRate * 100),
      cachesByType: Object.fromEntries(
        Object.entries(this.caches).map(([type, cache]) => [type, cache.size])
      )
    };
  }
}

// Global cache manager instance
let cacheManager = null;

/**
 * Initialize cache manager
 */
async function initializeCache(options = {}) {
  if (cacheManager) {
    console.log('CacheManager already initialized');
    return cacheManager;
  }
  
  cacheManager = new CacheManager(options);
  await cacheManager.init();
  
  // Export to global scope
  window.cacheManager = cacheManager;
  
  return cacheManager;
}

/**
 * Get cache manager instance
 */
function getCacheManager() {
  return cacheManager;
}

// Utility functions for easy access
function cacheGet(key, storageType) {
  return cacheManager ? cacheManager.get(key, storageType) : null;
}

function cacheSet(key, data, storageType, options) {
  return cacheManager ? cacheManager.set(key, data, storageType, options) : false;
}

function cacheDelete(key, storageType) {
  return cacheManager ? cacheManager.delete(key, storageType) : false;
}

// Auto-initialize if not in module environment
if (typeof module === 'undefined') {
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => initializeCache());
  } else {
    initializeCache();
  }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    CacheManager,
    initializeCache,
    getCacheManager,
    cacheGet,
    cacheSet,
    cacheDelete
  };
}
