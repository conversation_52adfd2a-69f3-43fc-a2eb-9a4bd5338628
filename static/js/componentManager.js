/**
 * ComponentManager - Handles dynamic loading of reusable components
 * Eliminates header/footer repetition across pages
 */
class ComponentManager {
  constructor(options = {}) {
    this.options = {
      headerSelector: 'header',
      footerSelector: 'footer',
      componentPath: './static/components/',
      enableCaching: true,
      enableSEO: true,
      ...options
    };

    this.componentCache = new Map();
    this.loadedComponents = new Set();
    this.initialized = false;

    // Component definitions
    this.components = {
      header: {
        file: 'header.html',
        selector: this.options.headerSelector,
        events: ['navigation', 'language', 'theme']
      },
      footer: {
        file: 'footer.html', 
        selector: this.options.footerSelector,
        events: ['social']
      }
    };
  }

  /**
   * Initialize component manager
   */
  async init() {
    if (this.initialized) return;

    console.log('Initializing ComponentManager...');

    try {
      // Load all components
      await this.loadAllComponents();
      
      // Initialize component-specific functionality
      this.initializeComponentEvents();
      
      this.initialized = true;
      console.log('ComponentManager initialized successfully');
      
      // Dispatch event for other modules
      document.dispatchEvent(new CustomEvent('componentsLoaded'));
      
    } catch (error) {
      console.error('Failed to initialize ComponentManager:', error);
    }
  }

  /**
   * Load all defined components
   */
  async loadAllComponents() {
    const loadPromises = Object.entries(this.components).map(([name, config]) => 
      this.loadComponent(name, config)
    );

    await Promise.all(loadPromises);
  }

  /**
   * Load a specific component
   */
  async loadComponent(name, config) {
    try {
      console.log(`Loading component: ${name}`);

      // Check if component container exists
      const container = document.querySelector(config.selector);
      if (!container) {
        console.warn(`Container not found for component ${name}: ${config.selector}`);
        return false;
      }

      // Check cache first
      let componentHTML = this.componentCache.get(name);
      
      if (!componentHTML) {
        // Load from file or generate dynamically
        componentHTML = await this.fetchComponent(name, config);
        
        if (this.options.enableCaching) {
          this.componentCache.set(name, componentHTML);
        }
      }

      // Insert component HTML
      container.innerHTML = componentHTML;
      
      // Mark as loaded
      this.loadedComponents.add(name);
      
      console.log(`Component ${name} loaded successfully`);
      return true;

    } catch (error) {
      console.error(`Failed to load component ${name}:`, error);
      return false;
    }
  }

  /**
   * Fetch component content
   */
  async fetchComponent(name, config) {
    // For now, generate components dynamically
    // In the future, this could load from external files
    switch (name) {
      case 'header':
        return this.generateHeaderComponent();
      case 'footer':
        return this.generateFooterComponent();
      default:
        throw new Error(`Unknown component: ${name}`);
    }
  }

  /**
   * Generate header component HTML
   */
  generateHeaderComponent() {
    return `
      <nav class="navbar navbar-expand-lg collapse show" id="upCollapse" role="navigation" aria-label="Main navigation">
        <div class="container-fluid">
          <a class="navbar-brand" href="." aria-label="iSHIUBA Homepage">
            <img src="static/img/ishiubahor.png" alt="iSHIUBA Logo" class="d-inline-block align-text-top" height="40" />
          </a>
          
          <!-- Mobile menu toggle button -->
          <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                  aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          
          <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
              <li id="Homepage" class="nav-item">
                <a class="nav-link active" href="." data-translate="Homepage" aria-current="page">Homepage</a>
              </li>
              <li id="Videos" class="nav-item">
                <a class="nav-link" href="main.html" data-translate="Videos">Videos</a>
              </li>
              <li id="About" class="nav-item">
                <a class="nav-link" href="about.html" data-translate="About">About</a>
              </li>
            </ul>
            
            <!-- Language and Theme Controls -->
            <div class="d-flex align-items-center gap-2">
              <!-- Language Selector -->
              <div class="btn-group" role="group" aria-label="Language Selector">
                <button type="button" class="btn btn-outline-primary btn-sm" data-language="en" title="English" aria-label="Switch to English">
                  <i class="fi fi-us me-1" aria-hidden="true"></i>
                  <span class="d-none d-md-inline">EN</span>
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" data-language="br" title="Português do Brasil" aria-label="Mudar para Português">
                  <i class="fi fi-br me-1" aria-hidden="true"></i>
                  <span class="d-none d-md-inline">PT</span>
                </button>
                <button type="button" class="btn btn-outline-danger btn-sm" data-language="jp" title="日本語 (Japanese)" aria-label="日本語に切り替え">
                  <i class="fi fi-jp me-1" aria-hidden="true"></i>
                  <span class="d-none d-md-inline">JP</span>
                </button>
              </div>
              
              <!-- Theme Selector -->
              <div class="btn-group" role="group" aria-label="Theme Selector">
                <button type="button" class="btn btn-outline-warning btn-sm theme-toggle-btn" data-theme-toggle data-theme-value="light" title="Light Theme" aria-label="Switch to light theme">
                  <i class="fa fa-sun" aria-hidden="true"></i>
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm theme-toggle-btn" data-theme-toggle data-theme-value="dark" title="Dark Theme" aria-label="Switch to dark theme">
                  <i class="fa fa-moon" aria-hidden="true"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </nav>
      
      <!-- Header Toggle Button -->
      <button id="up" class="btn btn-outline-secondary w-100 border-0 py-2" type="button" data-bs-toggle="collapse" data-bs-target="#upCollapse" aria-expanded="true" aria-controls="upCollapse" aria-label="Toggle navigation visibility">
        <i class="fa fa-chevron-up" aria-hidden="true"></i>
        <span class="visually-hidden">Hide navigation</span>
      </button>
    `;
  }

  /**
   * Generate footer component HTML
   */
  generateFooterComponent() {
    return `
      <!-- Footer Toggle Button -->
      <button id="down" class="btn btn-outline-secondary w-100 border-0 py-2" type="button" data-bs-toggle="collapse" data-bs-target="#downCollapse" aria-expanded="true" aria-controls="downCollapse" aria-label="Toggle footer visibility">
        <i class="fa fa-chevron-down" aria-hidden="true"></i>
        <span class="visually-hidden">Hide footer</span>
      </button>
      
      <!-- Footer Content -->
      <div class="collapse show" id="downCollapse">
        <div class="container-fluid py-4">
          <div class="row justify-content-center">
            <div class="col-12 col-md-8 col-lg-6">
              <div class="text-center">
                <p class="mb-2">
                  <small class="text-muted">Powered by NEXTせだい</small>
                </p>
                <p class="mb-0">
                  <small>
                    &copy; 2023 - <span id="currentYear">2024</span> IamSHIUBA. 
                    <span data-translate="footer">All rights reserved.</span>
                  </small>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Initialize component-specific events
   */
  initializeComponentEvents() {
    // Update current year in footer
    const currentYearElement = document.getElementById('currentYear');
    if (currentYearElement) {
      currentYearElement.textContent = new Date().getFullYear();
    }

    // Re-initialize navigation manager if loaded
    if (typeof initializeNavigation === 'function') {
      setTimeout(() => initializeNavigation(), 100);
    }

    // Re-initialize language manager if loaded
    if (typeof setupLanguageEventListeners === 'function') {
      setTimeout(() => setupLanguageEventListeners(), 100);
    }

    // Re-initialize theme manager if loaded
    if (typeof initializeTheme === 'function') {
      setTimeout(() => initializeTheme(), 100);
    }
  }

  /**
   * Reload a specific component
   */
  async reloadComponent(name) {
    if (!this.components[name]) {
      console.warn(`Component not found: ${name}`);
      return false;
    }

    // Clear cache
    this.componentCache.delete(name);
    this.loadedComponents.delete(name);

    // Reload
    return await this.loadComponent(name, this.components[name]);
  }

  /**
   * Check if component is loaded
   */
  isComponentLoaded(name) {
    return this.loadedComponents.has(name);
  }

  /**
   * Get loaded components
   */
  getLoadedComponents() {
    return Array.from(this.loadedComponents);
  }
}

// Global component manager instance
let componentManager = null;

/**
 * Initialize component manager
 */
async function initializeComponents(options = {}) {
  if (componentManager) {
    console.log('ComponentManager already initialized');
    return componentManager;
  }
  
  componentManager = new ComponentManager(options);
  await componentManager.init();
  
  // Export to global scope
  window.componentManager = componentManager;
  
  return componentManager;
}

/**
 * Get component manager instance
 */
function getComponentManager() {
  return componentManager;
}

// Auto-initialize if not in module environment
if (typeof module === 'undefined') {
  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => initializeComponents());
  } else {
    initializeComponents();
  }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    ComponentManager,
    initializeComponents,
    getComponentManager
  };
}
