/* ===================================
   Base Styles and Resets
   =================================== */

/* Apply theme colors to html and body */
html {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: 
    background-color var(--transition-normal) var(--ease-out),
    color var(--transition-normal) var(--ease-out);
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: 
    background-color var(--transition-normal) var(--ease-out),
    color var(--transition-normal) var(--ease-out);
}

/* Global link styles */
a {
  border-radius: 0.5rem;
  text-decoration: none;
  transition: all var(--transition-normal) var(--ease-out);
}

/* SVG color inheritance */
svg {
  color: var(--text-color);
  transition: color var(--transition-normal) var(--ease-out);
}

/* Global transition for interactive elements */
button,
.page-link {
  transition: all var(--transition-normal) var(--ease-out);
}

/* Container styles */
.container {
  background-color: var(--bg-color);
  color: var(--text-color);
}
