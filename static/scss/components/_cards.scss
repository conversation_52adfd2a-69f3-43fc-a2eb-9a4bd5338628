/* ===================================
   Card Component Styles
   =================================== */

/* Base card styles */
.card, .card.shadow-sm, .card.shadow-lg {
  transition: 
    transform var(--transition-fast) var(--ease-out),
    box-shadow var(--transition-fast) var(--ease-out),
    color var(--transition-normal) var(--ease-out);
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);

  .video-placeholder {
    background-color: var(--bg-color);
  }

  .card-title {
    color: var(--text-color);
    transition: color var(--transition-normal) var(--ease-out);
  }
}

/* Video Container Enhancements */
#videoContainer {
  min-height: 200px;
  transition: all var(--transition-normal) var(--ease-out);

  .card {
    animation: slideInUp 0.3s var(--ease-out) forwards;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px var(--shadow-color-d);
    }

    // Stagger animation for multiple cards
    &:nth-child(1) { animation-delay: var(--stagger-delay-1); }
    &:nth-child(2) { animation-delay: var(--stagger-delay-2); }
    &:nth-child(3) { animation-delay: var(--stagger-delay-3); }
    &:nth-child(4) { animation-delay: var(--stagger-delay-4); }
    &:nth-child(5) { animation-delay: var(--stagger-delay-5); }
    &:nth-child(6) { animation-delay: var(--stagger-delay-6); }
  }
}

/* Dark theme card hover adjustments */
[data-theme="dark"] #videoContainer .card:hover {
  box-shadow: 0 4px 12px oklch(from var(--color-white) l c h / 0.1);
}

/* Favorite Card Border */
.card.border-warning {
  border-width: 2px !important;
  position: relative;

  &::before {
    content: '⭐';
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--color-warning);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    z-index: 1;
  }
}

/* Responsive card adjustments */
@media (max-width: 576px) {
  #videoContainer .card {
    margin-bottom: 1rem;
  }
}
