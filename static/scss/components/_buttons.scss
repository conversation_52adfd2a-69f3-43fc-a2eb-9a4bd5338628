/* ===================================
   Button Component Styles
   =================================== */

/* Base button styles */
button {
  border: none;
  padding: 1rem;
  transition: all var(--transition-normal) var(--ease-out);
}

/* Enhanced <PERSON>trap <PERSON><PERSON> Styles with Theme Support */
.btn-primary {
  --bs-btn-font-weight: 600;
  --bs-btn-color: var(--bs-white);
  --bs-btn-bg: var(--accent-primary);
  --bs-btn-border-color: var(--accent-primary);
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-hover-bg: var(--accent-primary-hover);
  --bs-btn-hover-border-color: var(--accent-primary-hover);
  --bs-btn-active-color: var(--bs-white);
  --bs-btn-active-bg: var(--accent-primary-hover);
  --bs-btn-active-border-color: var(--accent-primary-hover);
}

/* Theme-aware outline buttons */
.btn-outline-primary {
  --bs-btn-color: var(--accent-primary);
  --bs-btn-border-color: var(--accent-primary);
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-hover-bg: var(--accent-primary);
  --bs-btn-hover-border-color: var(--accent-primary);
  --bs-btn-active-color: var(--bs-white);
  --bs-btn-active-bg: var(--accent-primary);
  --bs-btn-active-border-color: var(--accent-primary);
}

.btn-outline-secondary {
  --bs-btn-color: var(--text-color);
  --bs-btn-border-color: var(--border-color);
  --bs-btn-hover-color: var(--bg-color);
  --bs-btn-hover-bg: var(--text-color);
  --bs-btn-hover-border-color: var(--text-color);
  --bs-btn-active-color: var(--bg-color);
  --bs-btn-active-bg: var(--text-color);
  --bs-btn-active-border-color: var(--text-color);
}

/* Theme Toggle Button Styles */
.theme-toggle-btn {
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: all var(--transition-normal) var(--ease-out);

  &:hover {
    background-color: var(--hover-bg-color);
    border-color: var(--accent-primary);
    color: var(--accent-primary);
  }

  &.active {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
    color: var(--bs-white);
  }
}

/* Favorite Button Styles */
.favorite-btn {
  transition: all var(--transition-fast) var(--ease-out);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: scale(1.1);
  }

  &.active {
    background-color: var(--color-warning);
    border-color: var(--color-warning);
    color: var(--color-black);

    &:hover {
      background-color: var(--color-warning-hover);
      border-color: var(--color-warning-hover);
    }
  }
}

/* Load video button */
.load-video-btn {
  transition: all var(--transition-fast) var(--ease-out);

  &:hover {
    transform: scale(1.05);
  }
}

/* View Toggle Button */
#viewToggle {
  transition: all var(--transition-fast) var(--ease-out);
  border-radius: 0.375rem;

  &:hover {
    transform: scale(1.05);
  }
}

/* Refresh Button Animation */
#refreshButton {
  .fa-sync-alt {
    transition: transform var(--transition-normal) var(--ease-out);
  }

  &:hover .fa-sync-alt {
    transform: rotate(180deg);
  }
}

/* Responsive button adjustments */
@media (max-width: 576px) {
  .favorite-btn {
    width: 32px;
    height: 32px;
  }
}
