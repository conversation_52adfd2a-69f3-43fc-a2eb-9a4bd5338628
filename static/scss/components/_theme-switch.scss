/* ===================================
   Theme Switch Component Styles
   =================================== */

/* Enhanced Theme Switch with Better Theme Integration */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;

  input {
    opacity: 0;
    width: 0;
    height: 0;

    &:checked + .slider {
      background-color: var(--text-color);

      &:before {
        transform: translateX(20px);
        background-color: transparent;
        border-radius: 50%;
        border-top-color: transparent;
        border-left-color: transparent;
        border-right-color: transparent;
        box-shadow: inset -5px -3px 0 var(--bg-color);
      }
    }
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--hover-bg-color);
    transition: all var(--transition-slow) var(--ease-out);
    border-radius: 20px;
    border: 1px solid var(--border-color);
    box-shadow: 
      0 0 0.25em var(--shadow-color-d), 
      0.2px 0.2em 24px 0 var(--shadow-color-l);

    &:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 4px;
      bottom: 4px;
      background-color: oklch(0.85 0.15 85); /* Golden sun color */
      transition: all var(--transition-slow) var(--ease-out);
      border-radius: 50%;
      text-align: center;
      line-height: 16px;
      box-shadow: 0 0 0.25em var(--shadow-color-d);
    }
  }
}
