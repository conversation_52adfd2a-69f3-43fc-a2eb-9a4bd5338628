/* ===================================
   Enhanced Responsive Utilities
   =================================== */

/* ===== MOBILE FIRST BREAKPOINTS ===== */

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
  :root {
    /* Adjust spacing for mobile */
    --space-lg: 1rem;
    --space-xl: 1.5rem;
    --space-2xl: 2rem;

    /* Adjust typography for mobile */
    --font-size-4xl: 1.875rem; /* Smaller h1 on mobile */
    --font-size-3xl: 1.5rem;
    --font-size-2xl: 1.25rem;
  }

  /* Typography adjustments */
  h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
  }

  h2, h3, h4 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-normal);
  }

  /* Container adjustments */
  .container-fluid {
    padding-left: var(--space-md);
    padding-right: var(--space-md);
  }

  /* Navigation adjustments */
  .navbar {
    padding: var(--space-sm) var(--space-md);
  }

  .navbar-brand img {
    height: 2rem;
  }

  /* Card adjustments */
  .card {
    margin-bottom: var(--space-md);
  }

  /* Button adjustments */
  .btn {
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-sm);
  }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .navbar-nav {
    flex-direction: row;
    gap: var(--space-sm);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  :root {
    /* Restore larger spacing on tablets+ */
    --navbar-padding-x: var(--space-xl);
  }

  .navbar {
    padding: var(--space-md) var(--space-xl);
  }

  .navbar-nav {
    gap: var(--space-md);
  }

  /* Grid improvements */
  .row {
    margin-left: calc(var(--space-md) * -0.5);
    margin-right: calc(var(--space-md) * -0.5);
  }

  .col, [class*="col-"] {
    padding-left: calc(var(--space-md) * 0.5);
    padding-right: calc(var(--space-md) * 0.5);
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  /* Enhanced hover effects for desktop */
  #videoContainer .card {
    transition: transform var(--transition-normal) var(--ease-out),
                box-shadow var(--transition-normal) var(--ease-out);

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--card-hover-shadow);
    }
  }

  /* Better button hover effects */
  .btn {
    transition: all var(--transition-normal) var(--ease-out);

    &:hover {
      transform: translateY(-1px);
    }
  }

  /* Navigation enhancements */
  .nav-link {
    transition: all var(--transition-normal) var(--ease-out);

    &:hover {
      transform: translateY(-1px);
    }
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  :root {
    /* Larger spacing for large screens */
    --space-4xl: 8rem;
  }

  /* Enhanced typography scale */
  h1 {
    font-size: var(--font-size-5xl);
  }
}

/* Extra extra large devices (larger desktops, 1400px and up) */
@media (min-width: 1400px) {
  .container-fluid {
    max-width: 1320px;
    margin: 0 auto;
  }
}
