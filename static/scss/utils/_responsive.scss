/* ===================================
   Responsive Utilities
   =================================== */

/* Mobile-first responsive breakpoints */
@media (max-width: 576px) {
  /* Mobile adjustments already included in component files */
  
  /* Additional mobile-specific styles can be added here */
  h1 {
    font-size: 2.5rem;
  }
  
  h2, h3, h4 {
    font-size: 1.75rem;
  }
}

@media (min-width: 768px) {
  /* Tablet and up adjustments */
  .navbar-nav {
    flex-direction: row;
  }
}

@media (min-width: 992px) {
  /* Desktop adjustments */
  #videoContainer .card {
    &:hover {
      transform: translateY(-4px);
    }
  }
}
