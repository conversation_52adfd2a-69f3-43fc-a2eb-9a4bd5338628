/* ===================================
   Navbar Layout Styles
   =================================== */

.navbar {
  background-color: var(--navbar-bg-color);
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}

.nav-link {
  background-color: transparent;
  color: var(--text-color);
  border: none;
  transition: all var(--transition-normal) var(--ease-out);
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;

  &:hover {
    color: var(--accent-primary);
    background-color: var(--hover-bg-color);
    text-shadow: 0 0 3px var(--accent-primary);
  }

  &:focus {
    color: var(--accent-primary);
    background-color: var(--hover-bg-color);
  }
}

.navbar-nav {
  flex-direction: row;
}

.nav-item {
  padding-right: 1rem;
}

.navbar-brand {
  padding: 0.5rem;

  img {
    width: 100px;
  }
}

/* Language selector and switcher */
#langselect, #swicher {
  padding: 0.5rem;
}
