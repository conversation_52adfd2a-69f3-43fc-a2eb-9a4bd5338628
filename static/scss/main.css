@charset "UTF-8";
/* ===================================
   Main SCSS Entry Point
   =================================== */
/* ===================================
   CSS Custom Properties with OKLCH Colors
   =================================== */
/* OKLCH Color System
   OKLCH provides better perceptual uniformity and wider color gamut
   Format: oklch(lightness chroma hue / alpha)
   - Lightness: 0-1 (0 = black, 1 = white)
   - Chroma: 0+ (0 = grayscale, higher = more saturated)
   - Hue: 0-360 degrees
*/
:root {
  /* ===== PRIMARY COLORS ===== */
  /* Red accent colors using OKLCH */
  --accent-color: oklch(0.55 0.22 25); /* Bright red #ff0800 equivalent */
  --accent-hover-color: oklch(0.45 0.20 25); /* Darker red #cc0600 equivalent */
  --accent-dark-color: oklch(0.60 0.18 25); /* Dark theme red #ff4444 equivalent */
  --accent-dark-hover-color: oklch(0.70 0.16 25); /* Dark theme hover #ff6666 equivalent */
  /* ===== NEUTRAL COLORS ===== */
  /* Pure whites and blacks */
  --color-white: oklch(1.0 0 0); /* Pure white */
  --color-black: oklch(0.0 0 0); /* Pure black */
  /* Grays with slight warm tint */
  --color-gray-50: oklch(0.98 0.005 85);
  --color-gray-100: oklch(0.95 0.005 85);
  --color-gray-200: oklch(0.90 0.005 85);
  --color-gray-300: oklch(0.80 0.005 85);
  --color-gray-400: oklch(0.65 0.005 85);
  --color-gray-500: oklch(0.50 0.005 85);
  --color-gray-600: oklch(0.40 0.005 85);
  --color-gray-700: oklch(0.30 0.005 85);
  --color-gray-800: oklch(0.20 0.005 85);
  --color-gray-900: oklch(0.10 0.005 85);
  /* ===== SEMANTIC COLORS ===== */
  /* Warning/favorite colors */
  --color-warning: oklch(0.80 0.15 85); /* Golden yellow #ffc107 equivalent */
  --color-warning-hover: oklch(0.85 0.15 85); /* Lighter golden #ffcd39 equivalent */
  /* ===== ALPHA/TRANSPARENCY VALUES ===== */
  --alpha-light: 0.05;
  --alpha-medium: 0.125;
  --alpha-heavy: 0.25;
  --alpha-backdrop: 0.95;
  /* ===== ANIMATION AND TRANSITION VARIABLES ===== */
  /* Transition durations */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.4s;
  /* Easing functions */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  /* Animation delays for staggered effects */
  --stagger-delay-1: 0.1s;
  --stagger-delay-2: 0.2s;
  --stagger-delay-3: 0.3s;
  --stagger-delay-4: 0.4s;
  --stagger-delay-5: 0.5s;
  --stagger-delay-6: 0.6s;
  /* Bootstrap white reference */
  --bs-white: var(--color-white);
}

/* ===== LIGHT THEME VARIABLES ===== */
:root,
[data-theme=light] {
  /* Background colors */
  --bg-color: var(--color-white);
  --card-bg-color: var(--color-white);
  --navbar-bg-color: oklch(from var(--color-white) l c h / var(--alpha-backdrop));
  /* Text colors */
  --text-color: var(--color-black);
  --muted-text-color: var(--color-gray-500);
  /* Interactive colors */
  --accent-primary: var(--accent-color);
  --accent-primary-hover: var(--accent-hover-color);
  /* Border and shadow colors */
  --border-color: oklch(from var(--color-black) l c h / var(--alpha-medium));
  --hover-bg-color: oklch(from var(--color-black) l c h / var(--alpha-light));
  --shadow-color-d: oklch(from var(--color-black) l c h / 0.051);
  --shadow-color-l: oklch(from var(--color-white) l c h / var(--alpha-light));
  /* Focus ring colors */
  --focus-ring: oklch(from var(--accent-color) l c h / var(--alpha-heavy));
}

/* ===== DARK THEME VARIABLES ===== */
[data-theme=dark],
body.dark {
  /* Background colors */
  --bg-color: var(--color-black);
  --card-bg-color: var(--color-gray-900);
  --navbar-bg-color: oklch(from var(--color-black) l c h / var(--alpha-backdrop));
  /* Text colors */
  --text-color: var(--color-white);
  --muted-text-color: var(--color-gray-400);
  /* Interactive colors */
  --accent-primary: var(--accent-dark-color);
  --accent-primary-hover: var(--accent-dark-hover-color);
  /* Border and shadow colors */
  --border-color: oklch(from var(--color-white) l c h / var(--alpha-medium));
  --hover-bg-color: oklch(from var(--color-white) l c h / var(--alpha-light));
  --shadow-color-d: oklch(from var(--color-white) l c h / 0.051);
  --shadow-color-l: oklch(from var(--color-black) l c h / var(--alpha-light));
  /* Focus ring colors */
  --focus-ring: oklch(from var(--accent-dark-color) l c h / var(--alpha-heavy));
}

/* ===================================
   Base Styles and Resets
   =================================== */
/* Apply theme colors to html and body */
html {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color var(--transition-normal) var(--ease-out), color var(--transition-normal) var(--ease-out);
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color var(--transition-normal) var(--ease-out), color var(--transition-normal) var(--ease-out);
}

/* Global link styles */
a {
  border-radius: 0.5rem;
  text-decoration: none;
  transition: all var(--transition-normal) var(--ease-out);
}

/* SVG color inheritance */
svg {
  color: var(--text-color);
  transition: color var(--transition-normal) var(--ease-out);
}

/* Global transition for interactive elements */
button,
.page-link {
  transition: all var(--transition-normal) var(--ease-out);
}

/* Container styles */
.container {
  background-color: var(--bg-color);
  color: var(--text-color);
}

/* ===================================
   Typography Styles
   =================================== */
/* Headings and text alignment */
h1, h2, h3, h4, h5, p {
  text-align: center;
}

/* Main heading styles */
h1 {
  color: var(--accent-primary);
  font-size: 3rem;
  animation: fadeIn 3s var(--ease-in-out);
}

/* Secondary headings */
h2, h3, h4 {
  color: var(--text-color);
  font-size: 2rem;
  animation: fadeIn 3s var(--ease-in-out);
}

/* All headings animation */
h1, h2, h3, h4, h5 {
  animation: fadeIn 3s var(--ease-in-out);
}

/* Lead paragraph styles */
p.lead {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  animation: fadeIn 5s var(--ease-in-out);
}

/* ===================================
   Container Layout Styles
   =================================== */
/* Navigation buttons */
#up, #down {
  margin: 0;
  color: var(--accent-primary);
  padding: 2rem;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal) var(--ease-out);
}
#up:hover, #down:hover {
  background-color: var(--hover-bg-color);
  color: var(--accent-primary-hover);
  border-color: var(--accent-primary);
}

/* Video sections */
#youtube, .video-links {
  text-align: center;
}

.video-links {
  padding: 1rem;
  font-size: 1rem;
  font-weight: bold;
}
.video-links a {
  padding: 0.5rem;
  border-radius: 1rem;
  color: var(--accent-primary);
  transition: all var(--transition-normal) var(--ease-out);
  border: 2px solid transparent;
}
.video-links a:hover {
  border: 2px solid var(--accent-primary);
  color: var(--accent-primary-hover);
  background-color: var(--hover-bg-color);
}

/* Video iframe */
iframe {
  aspect-ratio: 16/9;
  animation: fadeIn 5s var(--ease-in-out);
}

/* Link animations */
.link {
  animation: fadeIn 7s var(--ease-in-out);
}

/* ===================================
   Navbar Layout Styles
   =================================== */
.navbar {
  background-color: var(--navbar-bg-color);
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}

.nav-link {
  background-color: transparent;
  color: var(--text-color);
  border: none;
  transition: all var(--transition-normal) var(--ease-out);
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
}
.nav-link:hover {
  color: var(--accent-primary);
  background-color: var(--hover-bg-color);
  text-shadow: 0 0 3px var(--accent-primary);
}
.nav-link:focus {
  color: var(--accent-primary);
  background-color: var(--hover-bg-color);
}

.navbar-nav {
  flex-direction: row;
}

.nav-item {
  padding-right: 1rem;
}

.navbar-brand {
  padding: 0.5rem;
}
.navbar-brand img {
  width: 100px;
}

/* Language selector and switcher */
#langselect, #swicher {
  padding: 0.5rem;
}

/* ===================================
   Button Component Styles
   =================================== */
/* Base button styles */
button {
  border: none;
  padding: 1rem;
  transition: all var(--transition-normal) var(--ease-out);
}

/* Enhanced Bootstrap Button Styles with Theme Support */
.btn-primary {
  --bs-btn-font-weight: 600;
  --bs-btn-color: var(--bs-white);
  --bs-btn-bg: var(--accent-primary);
  --bs-btn-border-color: var(--accent-primary);
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-hover-bg: var(--accent-primary-hover);
  --bs-btn-hover-border-color: var(--accent-primary-hover);
  --bs-btn-active-color: var(--bs-white);
  --bs-btn-active-bg: var(--accent-primary-hover);
  --bs-btn-active-border-color: var(--accent-primary-hover);
}

/* Theme-aware outline buttons */
.btn-outline-primary {
  --bs-btn-color: var(--accent-primary);
  --bs-btn-border-color: var(--accent-primary);
  --bs-btn-hover-color: var(--bs-white);
  --bs-btn-hover-bg: var(--accent-primary);
  --bs-btn-hover-border-color: var(--accent-primary);
  --bs-btn-active-color: var(--bs-white);
  --bs-btn-active-bg: var(--accent-primary);
  --bs-btn-active-border-color: var(--accent-primary);
}

.btn-outline-secondary {
  --bs-btn-color: var(--text-color);
  --bs-btn-border-color: var(--border-color);
  --bs-btn-hover-color: var(--bg-color);
  --bs-btn-hover-bg: var(--text-color);
  --bs-btn-hover-border-color: var(--text-color);
  --bs-btn-active-color: var(--bg-color);
  --bs-btn-active-bg: var(--text-color);
  --bs-btn-active-border-color: var(--text-color);
}

/* Theme Toggle Button Styles */
.theme-toggle-btn {
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: all var(--transition-normal) var(--ease-out);
}
.theme-toggle-btn:hover {
  background-color: var(--hover-bg-color);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}
.theme-toggle-btn.active {
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
  color: var(--bs-white);
}

/* Favorite Button Styles */
.favorite-btn {
  transition: all var(--transition-fast) var(--ease-out);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.favorite-btn:hover {
  transform: scale(1.1);
}
.favorite-btn.active {
  background-color: var(--color-warning);
  border-color: var(--color-warning);
  color: var(--color-black);
}
.favorite-btn.active:hover {
  background-color: var(--color-warning-hover);
  border-color: var(--color-warning-hover);
}

/* Load video button */
.load-video-btn {
  transition: all var(--transition-fast) var(--ease-out);
}
.load-video-btn:hover {
  transform: scale(1.05);
}

/* View Toggle Button */
#viewToggle {
  transition: all var(--transition-fast) var(--ease-out);
  border-radius: 0.375rem;
}
#viewToggle:hover {
  transform: scale(1.05);
}

/* Refresh Button Animation */
#refreshButton .fa-sync-alt {
  transition: transform var(--transition-normal) var(--ease-out);
}
#refreshButton:hover .fa-sync-alt {
  transform: rotate(180deg);
}

/* Responsive button adjustments */
@media (max-width: 576px) {
  .favorite-btn {
    width: 32px;
    height: 32px;
  }
}
/* ===================================
   Form Component Styles
   =================================== */
/* Input group text styling */
.input-group-text {
  transition: all var(--transition-fast) var(--ease-out);
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Input placeholder styles */
.input-group .form-control::-moz-placeholder {
  color: var(--muted-text-color);
}
.input-group .form-control::placeholder {
  color: var(--muted-text-color);
}

/* Search Input Enhancements */
#youtubeSearch {
  transition: all var(--transition-fast) var(--ease-out);
  background-color: var(--card-bg-color);
  border-color: var(--border-color);
  color: var(--text-color);
}
#youtubeSearch:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 0.2rem var(--focus-ring);
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* ===================================
   Card Component Styles
   =================================== */
/* Base card styles */
.card, .card.shadow-sm, .card.shadow-lg {
  transition: transform var(--transition-fast) var(--ease-out), box-shadow var(--transition-fast) var(--ease-out), color var(--transition-normal) var(--ease-out);
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
}
.card .video-placeholder, .card.shadow-sm .video-placeholder, .card.shadow-lg .video-placeholder {
  background-color: var(--bg-color);
}
.card .card-title, .card.shadow-sm .card-title, .card.shadow-lg .card-title {
  color: var(--text-color);
  transition: color var(--transition-normal) var(--ease-out);
}

/* Video Container Enhancements */
#videoContainer {
  min-height: 200px;
  transition: all var(--transition-normal) var(--ease-out);
}
#videoContainer .card {
  animation: slideInUp 0.3s var(--ease-out) forwards;
}
#videoContainer .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color-d);
}
#videoContainer .card:nth-child(1) {
  animation-delay: var(--stagger-delay-1);
}
#videoContainer .card:nth-child(2) {
  animation-delay: var(--stagger-delay-2);
}
#videoContainer .card:nth-child(3) {
  animation-delay: var(--stagger-delay-3);
}
#videoContainer .card:nth-child(4) {
  animation-delay: var(--stagger-delay-4);
}
#videoContainer .card:nth-child(5) {
  animation-delay: var(--stagger-delay-5);
}
#videoContainer .card:nth-child(6) {
  animation-delay: var(--stagger-delay-6);
}

/* Dark theme card hover adjustments */
[data-theme=dark] #videoContainer .card:hover {
  box-shadow: 0 4px 12px oklch(from var(--color-white) l c h/0.1);
}

/* Favorite Card Border */
.card.border-warning {
  border-width: 2px !important;
  position: relative;
}
.card.border-warning::before {
  content: "⭐";
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--color-warning);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  z-index: 1;
}

/* Responsive card adjustments */
@media (max-width: 576px) {
  #videoContainer .card {
    margin-bottom: 1rem;
  }
}
/* ===================================
   Theme Switch Component Styles
   =================================== */
/* Enhanced Theme Switch with Better Theme Integration */
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.switch input:checked + .slider {
  background-color: var(--text-color);
}
.switch input:checked + .slider:before {
  transform: translateX(20px);
  background-color: transparent;
  border-radius: 50%;
  border-top-color: transparent;
  border-left-color: transparent;
  border-right-color: transparent;
  box-shadow: inset -5px -3px 0 var(--bg-color);
}
.switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--hover-bg-color);
  transition: all var(--transition-slow) var(--ease-out);
  border-radius: 20px;
  border: 1px solid var(--border-color);
  box-shadow: 0 0 0.25em var(--shadow-color-d), 0.2px 0.2em 24px 0 var(--shadow-color-l);
}
.switch .slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: oklch(0.85 0.15 85); /* Golden sun color */
  transition: all var(--transition-slow) var(--ease-out);
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  box-shadow: 0 0 0.25em var(--shadow-color-d);
}

/* ===================================
   Video Player Component Styles
   =================================== */
/* Loading Animation Enhancements */
#loadingAnimation {
  animation: fadeIn 0.3s var(--ease-out);
}
#loadingAnimation .spinner-border {
  animation: spin 1s linear infinite;
}

/* ===================================
   Alert Component Styles
   =================================== */
/* Alert Enhancements */
.alert {
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: all var(--transition-normal) var(--ease-out);
}

/* ===================================
   Animation Utilities
   =================================== */
/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
/* ===================================
   Responsive Utilities
   =================================== */
/* Mobile-first responsive breakpoints */
@media (max-width: 576px) {
  /* Mobile adjustments already included in component files */
  /* Additional mobile-specific styles can be added here */
  h1 {
    font-size: 2.5rem;
  }
  h2, h3, h4 {
    font-size: 1.75rem;
  }
}
@media (min-width: 768px) {
  /* Tablet and up adjustments */
  .navbar-nav {
    flex-direction: row;
  }
}
@media (min-width: 992px) {
  /* Desktop adjustments */
  #videoContainer .card:hover {
    transform: translateY(-4px);
  }
}/*# sourceMappingURL=main.css.map */